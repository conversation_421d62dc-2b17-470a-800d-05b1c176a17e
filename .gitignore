# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables and secrets
.env
.env.local
.env.development
.env.production
.env.test
backend/.env
backend/.env.local
backend/.env.development
backend/.env.production
backend/.env.test

# Local environment files (never commit these)
**/.env.local
**/env.local
.env.*.local

# Development keypairs (never commit these)
**/dev-keypair.json
**/*-keypair.json

# API keys and secrets
**/secrets/
**/*secret*
**/*key*.json
**/*credentials*
**/wallet.json

# Logs that might contain sensitive data
*.log
logs/
**/*.log

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
.tmp/
temp/
