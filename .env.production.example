# Production Environment Configuration for SecureContract Pro
# Copy this file to .env.production and fill in your actual values

# Application Settings
NODE_ENV=production
VITE_APP_NAME=SecureContract Pro
VITE_APP_URL=https://contracts.yourcompany.com
VITE_API_URL=https://api.contracts.yourcompany.com

# Database Configuration
MONGO_PASSWORD=your_secure_mongodb_password_here
MONGO_URI=**************************************************************************************************

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_URL=redis://:your_secure_redis_password_here@redis:6379

# Security Secrets (Generate with: openssl rand -hex 32)
JWT_SECRET=your_jwt_secret_here_64_characters_minimum
SESSION_SECRET=your_session_secret_here_64_characters_minimum

# Email Configuration (Resend)
RESEND_API_KEY=re_your_resend_api_key_here

# Solana Configuration - MAINNET (PRODUCTION)
# ⚠️  WARNING: This is the production network using real SOL
SOLANA_CLUSTER=mainnet-beta
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
PLATFORM_FEE_RECIPIENT_PRIVATE_KEY=[your,mainnet,platform,fee,recipient,private,key,array]

# Frontend Configuration (Production)
VITE_API_URL=https://api.yourcompany.com
VITE_BACKEND_URL=https://api.yourcompany.com
VITE_SOLANA_CLUSTER=mainnet-beta

# Monitoring (Optional)
GRAFANA_PASSWORD=your_grafana_admin_password_here

# SSL/TLS Configuration
SSL_CERT_PATH=/etc/ssl/certs/your-domain.crt
SSL_KEY_PATH=/etc/ssl/private/your-domain.key

# CORS Configuration
CORS_ORIGIN=https://contracts.yourcompany.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,txt

# Analytics (Optional)
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
HOTJAR_ID=your_hotjar_id_here

# Error Tracking (Optional)
SENTRY_DSN=https://your-sentry-dsn-here

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
S3_BACKUP_BUCKET=your-backup-bucket-name
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
