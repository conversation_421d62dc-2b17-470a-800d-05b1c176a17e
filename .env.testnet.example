# Testnet Environment Configuration
NODE_ENV=production
PORT=3001

# Database Configuration (Use production-like setup)
MONGO_URI=***********************************************************************************************
REDIS_URL=redis://localhost:6379

# Solana Configuration - TESTNET
SOLANA_CLUSTER=testnet
SOLANA_RPC_URL=https://api.testnet.solana.com

# Platform Fee Configuration (Testnet keypair)
# Generate a new keypair for testnet: solana-keygen new
PLATFORM_FEE_RECIPIENT_PRIVATE_KEY=[your,testnet,platform,fee,recipient,private,key,array]

# Email Configuration (Testnet)
RESEND_API_KEY=your_testnet_resend_api_key_here

# Security Secrets (Generate with: openssl rand -hex 32)
JWT_SECRET=your_testnet_jwt_secret_here_64_characters_minimum
SESSION_SECRET=your_testnet_session_secret_here_64_characters_minimum

# Frontend Configuration
VITE_API_URL=https://api-testnet.yourcompany.com
VITE_BACKEND_URL=https://api-testnet.yourcompany.com
VITE_SOLANA_CLUSTER=testnet

# Production Features (Disabled for testnet)
ENABLE_SWAGGER=false
ENABLE_DEBUG_ROUTES=false
ENABLE_REQUEST_LOGGING=true
LOG_LEVEL=info

# CORS Configuration
CORS_ORIGIN=https://testnet.yourcompany.com

# Analytics (Testnet)
VITE_GA_TRACKING_ID=G-TESTNET_TRACKING_ID

# Monitoring
ENABLE_PERFORMANCE_MONITORING=true

# Rate Limiting (More restrictive than dev)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=200

# SSL/TLS Configuration (if using HTTPS)
SSL_CERT_PATH=/etc/ssl/certs/testnet-domain.crt
SSL_KEY_PATH=/etc/ssl/private/testnet-domain.key
