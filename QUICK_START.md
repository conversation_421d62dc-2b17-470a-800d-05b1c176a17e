# 🚀 Quick Start Guide - Digital Contract Platform

## 🌐 Network Environment Selection

SecureContract Pro now supports comprehensive Solana network environment selection with enhanced security features:
- **Devnet**: Development network with free SOL
- **Testnet**: Testing network with free SOL
- **Mainnet-Beta**: Production network with real SOL

📖 **See the [Network Environment Guide](./NETWORK_ENVIRONMENT_GUIDE.md) for detailed setup and usage instructions.**

## 🔧 Local Environment Setup

For local development, you'll need to set up `.env.local` files with actual configuration values:

### Quick Setup
```bash
# Run the interactive setup script
./scripts/setup-local-env.sh
```

📖 **See the [Local Environment Setup Guide](./LOCAL_ENVIRONMENT_SETUP.md) for detailed instructions.**

## 🎯 What's Been Completed

✅ **Complete End-to-End Implementation**
- ✅ Enhanced Solana Smart Contract with multi-party support
- ✅ Full-featured React frontend with wallet integration
- ✅ Robust Node.js backend with MongoDB
- ✅ Docker containerization for all services
- ✅ Comprehensive testing suite
- ✅ Production deployment scripts

## 🏃‍♂️ Get Started in 5 Minutes

### Option 1: Automated Setup (Recommended)
```bash
# Clone the repository
git clone <your-repo-url>
cd sign-contract

# Run the magic setup script
chmod +x scripts/setup-development.sh
./scripts/setup-development.sh
```

### Option 2: Docker Only (Fastest)
```bash
# Clone and start with Docker
git clone <your-repo-url>
cd sign-contract

# Copy environment files
cp .env.example .env
cp .env.example backend/.env

# Start everything with Docker
docker-compose up -d

# Install frontend dependencies and start
npm install
npm run dev
```

### Option 3: Manual Setup
```bash
# 1. Prerequisites
# - Node.js 18+
# - Docker & Docker Compose
# - Git

# 2. Clone and setup
git clone <your-repo-url>
cd sign-contract
cp .env.example .env
cp .env.example backend/.env

# 3. Install dependencies
npm install
cd backend && npm install && cd ..

# 4. Start services
docker-compose up -d mongodb redis ipfs

# 5. Start development servers
cd backend && npm run dev &
npm run dev
```

## 🌐 Access Your Application

After setup, access:
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **API Health**: http://localhost:3001/api/health
- **MongoDB**: mongodb://localhost:27017
- **Redis**: redis://localhost:6379
- **IPFS**: http://localhost:5001

## 🔗 Deploy Smart Contract to Solana Devnet

```bash
# Install Solana CLI (if not installed)
sh -c "$(curl -sSfL https://release.solana.com/v1.16.0/install)"
export PATH="$HOME/.local/share/solana/install/active_release/bin:$PATH"

# Install Anchor CLI (requires Rust)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
cargo install --git https://github.com/coral-xyz/anchor avm --locked --force
avm install latest && avm use latest

# Configure Solana for devnet
solana config set --url https://api.devnet.solana.com
solana-keygen new --no-bip39-passphrase
solana airdrop 2

# Deploy the smart contract
./scripts/deploy-contract.sh
```

## 💼 Using the Platform

### 1. Connect Your Wallet
- Install Phantom wallet extension
- Create or import a Solana wallet
- Switch to Devnet in wallet settings
- Click "Connect Wallet" in the app

### 2. Create a Contract
- Go to "Create Contract" page
- Fill in contract details:
  - Title and description
  - Agreement text
  - Party 1 & Party 2 details (names, emails, public keys)
  - Optional: Add mediator
  - Optional: Set expiry date
- Click "Create Contract"

### 3. Sign a Contract
- Use the contract ID to look up contracts
- Review contract details
- Connect wallet with the correct public key
- Click "Sign Contract"
- Approve the transaction in your wallet

## 🔧 Key Features Implemented

### Frontend (React + TypeScript)
- ✅ Wallet connection with Solana Wallet Adapter
- ✅ Contract creation form with validation
- ✅ Contract lookup and signing interface
- ✅ Real-time status updates
- ✅ Responsive design with Tailwind CSS
- ✅ Toast notifications for user feedback

### Backend (Node.js + Express)
- ✅ RESTful API with comprehensive endpoints
- ✅ MongoDB integration with Mongoose
- ✅ User management and authentication
- ✅ Contract CRUD operations
- ✅ Audit logging for all actions
- ✅ Error handling and validation

### Smart Contract (Solana + Anchor)
- ✅ Multi-party contract support (2-10 parties)
- ✅ Digital signature tracking
- ✅ Contract status management
- ✅ Mediator functionality
- ✅ Expiry date handling
- ✅ Platform fee collection
- ✅ Event emission for frontend updates

### Database (MongoDB)
- ✅ Contract schema with full metadata
- ✅ User profiles and statistics
- ✅ Audit trail for compliance
- ✅ Optimized indexes for performance
- ✅ Data validation and constraints

### DevOps & Deployment
- ✅ Docker containerization
- ✅ Docker Compose for development
- ✅ Environment configuration
- ✅ Automated deployment scripts
- ✅ Production-ready setup
- ✅ Health checks and monitoring

## 📊 API Endpoints

### Contract Management
- `POST /api/contracts` - Create new contract
- `GET /api/contracts/:id` - Get contract details
- `POST /api/contracts/:id/sign` - Sign contract
- `GET /api/contracts/user/:publicKey` - Get user's contracts

### User Management
- `POST /api/users` - Create/update user profile
- `GET /api/users/:publicKey` - Get user profile

### System
- `GET /api/health` - Health check
- `GET /` - API information

## 🔐 Security Features

- ✅ Wallet-based authentication
- ✅ Input validation and sanitization
- ✅ Rate limiting protection
- ✅ CORS configuration
- ✅ Environment variable security
- ✅ Database connection security
- ✅ Smart contract access controls

## 🧪 Testing

```bash
# Frontend tests
npm run test

# Backend tests
cd backend && npm run test

# Smart contract tests
cd solana-contract/digital_contract
anchor test
```

## 🚀 Production Deployment

```bash
# Deploy to production
./scripts/deploy-production.sh

# Deploy smart contract to mainnet
# (Update SOLANA_CLUSTER=mainnet-beta first)
./scripts/deploy-contract.sh
```

## 📚 Documentation

- `README.md` - Project overview and setup
- `DEPLOYMENT.md` - Comprehensive deployment guide
- `QUICK_START.md` - This quick start guide
- `.env.example` - Environment configuration template

## 🆘 Troubleshooting

### Common Issues
1. **Port conflicts**: Ensure ports 3001, 5173, 27017 are free
2. **Docker issues**: Run `docker-compose down` and `docker-compose up -d`
3. **Wallet connection**: Ensure you're on the correct network (devnet)
4. **Smart contract**: Ensure sufficient SOL balance for deployment

### Debug Commands
```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs -f backend
docker-compose logs -f mongodb

# Check API health
curl http://localhost:3001/api/health

# Check Solana connection
solana config get
solana balance
```

## 🎉 Success!

Your Digital Contract Platform is now running! You have:
- ✅ A fully functional dApp for digital contracts
- ✅ Solana blockchain integration
- ✅ Multi-party contract support
- ✅ Secure wallet authentication
- ✅ Production-ready deployment
- ✅ Comprehensive documentation

Start creating and signing digital contracts on the Solana blockchain! 🚀
